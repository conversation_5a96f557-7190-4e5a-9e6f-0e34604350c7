package de.dasjeff.aSMPVCore;

import com.google.inject.Inject;
import com.velocitypowered.api.event.proxy.ProxyInitializeEvent;
import com.velocitypowered.api.event.proxy.ProxyShutdownEvent;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.plugin.Plugin;
import com.velocitypowered.api.plugin.annotation.DataDirectory;
import com.velocitypowered.api.proxy.ProxyServer;
import de.dasjeff.aSMPVCore.managers.*;
import de.dasjeff.aSMPVCore.commands.CoreCommand;
import org.slf4j.Logger;

import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Plugin(
    id = "asmp-vcore",
    name = "ASMP-VCore",
    version = BuildConstants.VERSION,
    description = "Core system for network-wide communication between proxy, paper servers and webpanel.",
    authors = {"DasJeff"}
)
public class ASMPVCore {

    // Core Managers - Thread-safe initialization
    private volatile ConfigManager configManager;
    private volatile DatabaseManager databaseManager;
    private volatile CacheManager cacheManager;
    private volatile de.dasjeff.aSMPVCore.managers.SecurityManager securityManager;
    private volatile CommandManager commandManager;
    private volatile ListenerManager listenerManager;
    private volatile ModuleManager moduleManager;

    // Thread safety for initialization
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final CountDownLatch initializationLatch = new CountDownLatch(1);
    private volatile boolean initializationFailed = false;

    @Inject
    private Logger logger;

    @Inject
    private ProxyServer proxyServer;

    @Inject
    @DataDirectory
    private Path dataDirectory;

    @Subscribe
    public void onProxyInitialization(ProxyInitializeEvent event) {
        logger.info("Loading {} v{}", "ASMP-VCore", BuildConstants.VERSION);

        // Initialize managers in correct order
        initializeManagers();
    }

    @Subscribe
    public void onProxyShutdown(ProxyShutdownEvent event) {
        logger.info("Shutting down ASMP-VCore...");

        // Shutdown managers in reverse order
        shutdownManagers();

        logger.info("ASMP-VCore has been disabled successfully!");
    }

    private void initializeManagers() {
        CompletableFuture.runAsync(() -> {
            try {
                // 1. ConfigManager
                configManager = new ConfigManager(this);
                logger.info("ConfigManager initialized.");

                // 2. ModuleManager
                moduleManager = new ModuleManager(this);
                logger.info("ModuleManager initialized.");

                // 3. DatabaseManager
                if (configManager.getMainConfig().getBoolean("database.enabled", true)) {
                    databaseManager = new DatabaseManager(this);
                    if (!databaseManager.initialize()) {
                        logger.error("Failed to initialize DatabaseManager - disabling plugin functionality requiring database access.");
                    } else {
                        logger.info("DatabaseManager initialized.");
                    }
                } else {
                    logger.info("Database is disabled in configuration.");
                }

                // 4. CacheManager
                cacheManager = new CacheManager(this);
                logger.info("CacheManager initialized.");

                // 5. SecurityManager
                securityManager = new de.dasjeff.aSMPVCore.managers.SecurityManager(this);
                logger.info("SecurityManager initialized.");

                // 6. CommandManager
                commandManager = new CommandManager(this);
                logger.info("CommandManager initialized.");

                // 7. ListenerManager
                listenerManager = new ListenerManager(this);
                logger.info("ListenerManager initialized.");

                // 8. Register core commands
                registerCoreCommands();

                // 9. Load and enable modules
                moduleManager.loadModules();
                moduleManager.enableModules();

                // Mark initialization as complete
                initialized.set(true);
                initializationLatch.countDown();

                logger.info("ASMP-VCore has been enabled successfully!");
                logger.info("ASMP-VCore by DasJeff is ready!");
                logger.info("Running on Velocity Version: {}", proxyServer.getVersion().getVersion());

            } catch (Exception e) {
                logger.error("Failed to initialize ASMP-VCore!", e);
                initializationFailed = true;
                initializationLatch.countDown();
            }
        });
    }

    private void shutdownManagers() {
        try {
            // 1. Disable all modules
            if (moduleManager != null) {
                moduleManager.disableModules();
                logger.info("All modules disabled.");
            }

            // 2. Unregister commands
            if (commandManager != null) {
                commandManager.unregisterAllCommands();
                logger.info("All commands unregistered.");
            }

            // 3. Unregister listeners
            if (listenerManager != null) {
                listenerManager.unregisterAllListeners();
                logger.info("All listeners unregistered.");
            }

            // 4. Shutdown SecurityManager
            if (securityManager != null) {
                logger.info("SecurityManager stats: {}", securityManager.getSecurityStats());
                securityManager.shutdown();
                logger.info("SecurityManager shutdown.");
            }

            // 5. Shutdown CacheManager
            if (cacheManager != null) {
                cacheManager.shutdown();
                logger.info("CacheManager shutdown.");
            }

            // 6. Shutdown DatabaseManager
            if (databaseManager != null) {
                databaseManager.closeDataSource();
                logger.info("DatabaseManager shutdown.");
            }

        } catch (Exception e) {
            logger.error("Error during shutdown!", e);
        }
    }

    private void registerCoreCommands() {
        try {
            // Register the main core command
            CoreCommand coreCommand = new CoreCommand(this);
            commandManager.registerCommand(coreCommand, "vcore", "asmpvcore");
            logger.info("Core commands registered successfully.");
        } catch (Exception e) {
            logger.error("Failed to register core commands!", e);
        }
    }

    // Thread-safe getters for managers
    public ConfigManager getConfigManager() {
        waitForInitialization();
        return configManager;
    }

    public DatabaseManager getDatabaseManager() {
        waitForInitialization();
        if (databaseManager == null) {
            logger.warn("Attempted to access DatabaseManager, but it was not initialized (likely due to missing or default configuration).");
        }
        return databaseManager;
    }

    public CacheManager getCacheManager() {
        waitForInitialization();
        return cacheManager;
    }

    public de.dasjeff.aSMPVCore.managers.SecurityManager getSecurityManager() {
        waitForInitialization();
        return securityManager;
    }

    public CommandManager getCommandManager() {
        waitForInitialization();
        return commandManager;
    }

    public ListenerManager getListenerManager() {
        waitForInitialization();
        return listenerManager;
    }

    public ModuleManager getModuleManager() {
        waitForInitialization();
        return moduleManager;
    }

    /**
     * Waits for initialization to complete before returning managers.
     * Prevents race conditions when accessing managers during startup.
     */
    private void waitForInitialization() {
        if (!initialized.get() && !initializationFailed) {
            try {
                boolean completed = initializationLatch.await(30, TimeUnit.SECONDS);
                if (!completed) {
                    logger.error("Initialization timeout - managers may not be fully initialized!");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("Interrupted while waiting for initialization", e);
            }
        }
    }

    /**
     * Checks if the plugin is fully initialized.
     * @return true if initialization completed successfully, false otherwise.
     */
    public boolean isInitialized() {
        return initialized.get() && !initializationFailed;
    }

    public Logger getLogger() {
        return logger;
    }

    public ProxyServer getProxyServer() {
        return proxyServer;
    }

    public Path getDataDirectory() {
        return dataDirectory;
    }
}
