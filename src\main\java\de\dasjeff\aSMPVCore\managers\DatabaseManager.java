package de.dasjeff.aSMPVCore.managers;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.ConfigManager.ConfigWrapper;
import de.dasjeff.aSMPVCore.util.ConfigValidator;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * Manages database connections using HikariCP connection pooling.
 * Provides thread-safe database access for the ASMP-VCore plugin.
 */
public class DatabaseManager {

    private final ASMPVCore corePlugin;
    private HikariDataSource dataSource;

    // Configuration values
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;
    private final int poolSize;
    private final long connectionTimeout;
    private final long idleTimeout;
    private final long maxLifetime;

    public DatabaseManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;

        ConfigWrapper config = corePlugin.getConfigManager().getMainConfig();

        // Load database configuration
        this.host = config.getString("database.host", "localhost");
        this.port = config.getInt("database.port", 3306);
        this.database = config.getString("database.database", "asmp_vcore");
        this.username = config.getString("database.username", "root");
        this.password = config.getString("database.password", "your_password");
        this.poolSize = config.getInt("database.pool_size", 10);
        this.connectionTimeout = config.getLong("database.connection_timeout", 30000);
        this.idleTimeout = config.getLong("database.idle_timeout", 600000);
        this.maxLifetime = config.getLong("database.max_lifetime", 1800000);
    }

    /**
     * Initializes the database connection pool.
     * @return true if initialization succeeded and config is valid; false to disable plugin functionality.
     */
    public boolean initialize() {
        try {
            // Validate database configuration
            ConfigValidator.validateDatabaseConfig(corePlugin.getConfigManager().getMainConfig());
        } catch (IllegalArgumentException e) {
            corePlugin.getLogger().error("==================================================");
            corePlugin.getLogger().error("ASMP-VCore DATABASE CONFIGURATION ERROR!");
            corePlugin.getLogger().error("Error: {}", e.getMessage());
            corePlugin.getLogger().error("Please fix your database configuration in config.yml");
            corePlugin.getLogger().error("==================================================");
            return false;
        }

        // Check for default configuration values
        boolean defaultConfig = host.equals("localhost")
                && port == 3306
                && database.equals("asmp_vcore")
                && username.equals("root")
                && password.equals("your_password");

        if (defaultConfig) {
            corePlugin.getLogger().error("==================================================");
            corePlugin.getLogger().error("ASMP-VCore DETECTED DEFAULT DATABASE CONFIGURATION!");
            corePlugin.getLogger().error("Please configure your database settings in config.yml");
            corePlugin.getLogger().error("Current settings:");
            corePlugin.getLogger().error("  Host: {}", host);
            corePlugin.getLogger().error("  Port: {}", port);
            corePlugin.getLogger().error("  Database: {}", database);
            corePlugin.getLogger().error("  Username: {}", username);
            corePlugin.getLogger().error("==================================================");
            return false;
        }

        return setupDataSource();
    }

    private boolean setupDataSource() {
        HikariConfig hikariConfig = new HikariConfig();

        // Basic connection settings
        hikariConfig.setJdbcUrl("jdbc:mariadb://" + host + ":" + port + "/" + database);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName("org.mariadb.jdbc.Driver");

        // Pool settings
        hikariConfig.setMaximumPoolSize(poolSize);
        hikariConfig.setMinimumIdle(Math.max(1, poolSize / 4));
        hikariConfig.setConnectionTimeout(connectionTimeout);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setMaxLifetime(maxLifetime);
        hikariConfig.setLeakDetectionThreshold(60000);

        // Connection properties for performance
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true");
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true");
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true");
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true");
        hikariConfig.addDataSourceProperty("maintainTimeStats", "false");

        // Connection validation
        hikariConfig.setConnectionTestQuery("SELECT 1");
        hikariConfig.setValidationTimeout(5000);

        // Pool name for monitoring
        hikariConfig.setPoolName("ASMP-VCore-Pool");

        try {
            this.dataSource = new HikariDataSource(hikariConfig);
            corePlugin.getLogger().info("Successfully established database connection pool to {}:{}/{}", host, port, database);

            // Test the connection
            try (Connection conn = getConnection()) {
                corePlugin.getLogger().info("Database connection test successful");
                return true;
            }
        } catch (Exception e) {
            corePlugin.getLogger().error("Could not establish database connection pool! Plugin functionality requiring database access will be disabled.", e);
            this.dataSource = null;
            return false;
        }
    }

    /**
     * Gets a connection from the pool.
     * @return A database connection.
     * @throws SQLException if no connection is available or database is not initialized.
     */
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("Database source is not available. Check plugin logs for connection errors.");
        }
        return dataSource.getConnection();
    }

    /**
     * Checks if the database is connected and available.
     * @return true if connected, false otherwise.
     */
    public boolean isConnected() {
        return dataSource != null && !dataSource.isClosed();
    }

    /**
     * Gets the current pool statistics.
     * @return A string containing pool statistics, or null if not available.
     */
    public String getPoolStats() {
        if (dataSource == null) {
            return "Database not initialized";
        }

        return String.format("Pool Stats - Active: %d, Idle: %d, Total: %d, Waiting: %d",
                dataSource.getHikariPoolMXBean().getActiveConnections(),
                dataSource.getHikariPoolMXBean().getIdleConnections(),
                dataSource.getHikariPoolMXBean().getTotalConnections(),
                dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
    }

    /**
     * Closes the data source and all connections.
     * Should be called during plugin shutdown.
     */
    public void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            corePlugin.getLogger().info("Database connection pool closed.");
        }
    }

    /**
     * Executes a database operation asynchronously.
     * @param operation The database operation to execute.
     */
    public void executeAsync(DatabaseOperation operation) {
        corePlugin.getProxyServer().getScheduler()
                .buildTask(corePlugin, () -> {
                    try (Connection conn = getConnection()) {
                        operation.execute(conn);
                    } catch (SQLException e) {
                        corePlugin.getLogger().error("Database operation failed", e);
                    }
                })
                .schedule();
    }

    /**
     * Functional interface for database operations.
     */
    @FunctionalInterface
    public interface DatabaseOperation {
        void execute(Connection connection) throws SQLException;
    }
}
