# Konzept: AdventureSMP Punishment Modul (Velocity Core)

## 1. Kernprinzipien

* **Modular & Konfigurierbar:** Admins definieren Strafarten, Gründe und Stufen per Config.
* **Staff-Zentrierte Benutzerfreundlichkeit:** Intuitive Ingame-GUI, klare An<PERSON>n, minimaler Befehlsaufwand.
* **Nachvollziehbarkeit:** Klare Strafhistorie und Stufenlogik.
* **Stufenbasiertes System:** Vorfälle gleicher Art führen zu höheren Stufen innerhalb eines Templates.
* **Direkte API-Integration:** Nutzung der Voicechat-API.
* **Fokus auf Ingame-Verwaltung:** Webpanel ist sekundär für die Erstentwicklung.

## 2. Der Zentrale Befehl: `/punish <SpielerName>`

* **Permission:** `asmp.vcore.punishment.access`
* **Funktion:** Dies ist der Haupteinstiegspunkt für Staff. Öffnet eine GUI, die auf den Zielspieler zugeschnitten ist.
* **GUI-Aufbau – "Spielerakte: {SpielerName}"**:
    * **Header-Bereich:**
        * Spielername, UUID
        * Aktuelle IP-Adresse(n) des Spielers (letzte bekannte)
        * Anzahl bisheriger Bestrafungen (Gesamtzahl)
        * Button: `Historie anzeigen` (öffnet die `/history <SpielerName>` GUI für diesen Spieler)
        * Optional (für später mit Report-System): Anzeige offener Reports gegen diesen Spieler.
    * **Aktionsauswahl (z.B. als Icons/Tabs unter dem Header):**
        * `Chat Mute`
        * `Voice Mute`
        * `Ban`
    * **Template-Liste (dynamisch je nach gewählter Aktion):**
        * Eine scrollbare Liste der für diese Aktion verfügbaren Templates.
        * **Pro Template-Eintrag in der Liste:**
            * Name des Templates (z.B. "Chat: Leichte Beleidigung")
            * **Alle Stufen des Templates direkt sichtbar aufgelistet:**
                * Stufe 1: Dauer, Grund-Auszug
                * Stufe 2: Dauer, Grund-Auszug
                * ...
            * **Hervorhebung der aktuell fälligen Stufe:** Basierend auf der Historie des Spielers für *genau dieses Template* wird die Stufe, die jetzt angewendet würde, visuell hervorgehoben.
        * Klick auf ein Template wählt dieses für die Bestrafung aus.
    * **Option: "Manuelle Strafe" (Button unter der Template-Liste):**
        * Felder für manuelle Dauer (z.B. "5h", "3d", "perm") und einen individuellen Grund. Manuelle Strafen haben keine "Stufen" und beeinflussen die Stufenzählung der Templates nicht.
    * **Bestätigungsbereich (nach Auswahl eines Templates oder bei manueller Strafe):**
        * Anzeige: Spieler, Aktion, Grund (vom Template/manuell), Dauer, angewandte Stufe (falls Template).
        * Feld für **interne Notiz** des Staff-Mitglieds (nicht für Spieler sichtbar, wird in der History gespeichert).
        * Button: `Bestrafung ausführen` / `Abbrechen`.

## 3. Template-Konfiguration (`punishments.yml`)

Die Konfigurationsdatei `punishments.yml` befindet sich im Plugin-Ordner des Core-Plugins. Da es ein Modul ist sollte es in einem Unterordner des Plugins abgelegt werden beispielsweise in `PunishmentSystem`. Ich denke es ist auch sinnvoll, die Einstellungen für die Nachrichten, also `player_notifications` in eine eigene Datei auszulagern, beispielsweise `messages.yml`. Und falls sinnvoll eine weitere für allgemeine Einstellungen, beispielsweise `config.yml`. Für die Permissions sollte es auch extra Permissions geben, wie beispielsweise `asmp.vcore.punishment.template.*` und so weiter. Dafür sollte auch ein PermissionUtil erstellt werden, ähnlich wie in dem Paper AdventureSMP-Core.

```yaml
# punishments.yml

# Globale Nachrichtenformate für Spieler
player_notifications:
  mute: "&cDu wurdest für {duration} stummgeschaltet. Grund: {reason}"
  temporary_ban: "&cDu wurdest für {duration} von AdventureSMP gebannt. Grund: {reason}"
  permanent_ban: "&cDu wurdest permanent von AdventureSMP gebannt. Grund: {reason}"
  voice_mute: "&cDu wurdest im Voice-Chat für {duration} gemuted. Grund: {reason}"

# Definition der Straf-Templates
punishment_templates:
  chat_spam:
    display_name: "Chat: Spamming"
    type: MUTE # MUTE, VOICE_MUTE, BAN
    permission_node: "asmp.vcore.punishment.template.chat_spam" # Beispiel für spezifische Permission pro Template
    stufen:
      - id: 1 # Eindeutige ID der Stufe innerhalb des Templates
        duration: "10m"
        reason_template: "Spamming im Chat" # Dies füllt {reason} in player_notifications
      - id: 2
        duration: "1h"
        reason_template: "Wiederholtes Spamming im Chat"
      - id: 3
        duration: "6h"
        reason_template: "Mehrfaches Spamming im Chat"

  voice_noise:
    display_name: "Voice: Störende Geräusche"
    type: VOICE_MUTE
    permission_node: "asmp.vcore.punishment.template.voice_noise"
    stufen:
      - id: 1
        duration: "30m"
        reason_template: "Störende Geräusche im Voicechat"
      - id: 2
        duration: "2h"
        reason_template: "Wiederholt störende Geräusche im Voicechat"
      - id: 3
        duration: "permanent"
        reason_template: "Permanenter Voicechat-Ausschluss wegen wiederholter Störung"

  ban_hacking:
    display_name: "Ban: Hacking / Client-Modifikationen"
    type: BAN
    ip_ban: true # Soll diese Art von Ban auch die IP des Spielers bannen?
    permission_node: "asmp.vcore.punishment.template.ban_hacking"
    stufen:
      - id: 1
        duration: "permanent"
        reason_template: "Nutzung unerlaubter Client-Modifikationen (Hacking)"

# Einstellungen für Discord-Benachrichtigungen
discord_notifications:
  enabled: true
  webhook_url: "DEIN_DISCORD_WEBHOOK_URL_HIER"
  notify_on: # Welche Aktionen sollen gemeldet werden?
    - BAN_PERMANENT
    - BAN_TEMPORARY
    - 
    # - MUTE_PERMANENT # Falls Mutes permanent sein können
    # - MUTE_LONGER_THAN_7_DAYS # Beispiel für spezifischere Meldung
  embed_color: "#FF0000" # Hex-Farbcode für die Discord Embeds
  # message_format: "**{staffName}** hat **{playerName}** ({playerUUID}) {actionType} für **{duration}**. Grund: _{reason}_" # Beispiel
```

### Platzhalter und Logik:

* **`reason_template` Platzhalter:** `{playerName}`, `{staffName}`, `{date}` (formatiert DD.MM.YYYY HH:mm:ss), `{tier}` (Nummer der Stufe).
* **`player_notifications` Platzhalter:** `{duration}`, `{reason}` (aus dem `reason_template` der Stufe).
* **IP-Ban Logik:**
    * Wenn `ip_ban: true` und ein Ban ausgeführt wird, wird die aktuelle IP-Adresse des Spielers (von Velocity `Player#getRemoteAddress()`) zusammen mit der UUID des Spielers gebannt.
    * IP-Bans werden separat von UUID-Bans gespeichert, aber verknüpft. Ein IP-Ban-Eintrag sollte die UUID(s) enthalten, die zu diesem IP-Ban geführt haben.
    * Beim Verbindungsversuch: Zuerst UUID-Check, dann IP-Check.
    * Das Aufheben eines UUID-Bans sollte optional anbieten, auch verknüpfte IP-Bans aufzuheben, *falls keine anderen gebannten UUIDs mit dieser IP assoziiert sind*.

## 4. History-Management (`/history <SpielerName>`)

* **Permission (eigene History):** Standardmäßig mit `asmp.vcore.punishment.access`
* **Permission (fremde History):** `asmp.vcore.punishment.history.viewothers`
* **Aufruf:** `/history <SpielerName>` oder über Button in `/punish` GUI.
* **GUI-Oberfläche:** Chronologische Liste aller Vorfälle (neuste zuerst).
    * **Pro Eintrag:** ID des Vorfalls, Datum & Uhrzeit, Typ (Mute, Voice-Mute, Ban), Grund (der dem Spieler angezeigt wurde), Dauer (oder "Permanent"), Ausgeführt von (Staff-Mitglied), Status (Aktiv, Abgelaufen, Aufgehoben/Pardoned), Interne Notiz des Staff-Mitglieds (Tooltip oder Detailansicht, Permission: `asmp.vcore.punishment.viewinternalnotes`).
    * **Filteroptionen:**
        * Nach Typ (Mute, Voice-Mute, Ban)
        * Textsuche im Grund / Ausführender Staff
    * **Aktionen (mit spezifischen Permissions):**
        * `Details anzeigen`: Zeigt alle Infos inkl. interner Notiz und ggf. gebannter IP.
        * `Aufheben/Pardon` (`asmp.vcore.punishment.pardon`): Markiert Strafe als aufgehoben. Setzt die Stufenzählung für das *spezifische Template dieses Vorfalls* für den Spieler zurück. Strafe bleibt als "aufgehoben" sichtbar.
        * `Löschen` (`asmp.vcore.punishment.delete`): Entfernt Eintrag komplett. Setzt Stufenzählung zurück. *Sehr restriktiv!*
        * `Bearbeiten` (`asmp.vcore.punishment.edit`): Ändern von Dauer/Grund (mit Log der Änderung).

## 5. Voicechat API Integration

* Das System interagiert direkt mit der API des Voicechat-Plugins (`https://voicechat.modrepo.de/`).
* **Annahmen für die API (Beispielhaft – genaue Methodennamen aus Doku entnehmen!):**
    * `VoiceChatAPI.mutePlayer(UUID playerUUID, long durationMillis, String reason)`
    * `VoiceChatAPI.unmutePlayer(UUID playerUUID)`
    * `VoiceChatAPI.isPlayerMuted(UUID playerUUID)`
* Das Punishment-Modul konvertiert die Dauer aus den Templates (z.B. "30m") in das von der API erwartete Format (wahrscheinlich Millisekunden).
* Der Grund wird ebenfalls an die API übergeben, falls diese das unterstützt und anzeigt.

## 6. Berechtigungen (Permissions)

Alle Permissions nutzen das Präfix `asmp.vcore.punishment.`.

* `access`: Grundlegender Zugriff auf `/punish <SpielerName>` und `/history <eigenerName>`.
* `history.viewothers`: Zugriff auf `/history <andererSpielerName>`.
* `template.<template_name>`: Erlaubt die Nutzung eines spezifischen Templates (z.B. `template.chat_spam`).
    * Alternativ Gruppen: `template.chat.*`, `template.ban.*`, `template.voicemute.*`
* `custom.mute`: Erlaubt manuelle Mutes.
* `custom.voicemute`: Erlaubt manuelle Voice-Mutes.
* `custom.ban`: Erlaubt manuelle Bans.
* `pardon`: Erlaubt das Aufheben ("Pardon") von Strafen.
* `edit`: Erlaubt das Bearbeiten von Strafen in der History.
* `delete`: Erlaubt das vollständige Löschen von Strafen aus der History.
* `viewinternalnotes`: Erlaubt das Einsehen interner Notizen zu Strafen.
* `addinternalnotes`: Erlaubt das Hinzufügen interner Notizen beim Erstellen einer Strafe.
* `config.reload`: Erlaubt das Neuladen der `punishments.yml` via Befehl (z.B. `/punishadmin reload`).

## 7. Staff Performance Tracking & Automatisierte Vorschläge

* **Staff Tracking (intern):** Aktionen (ausgeführte Strafen, Änderungen) werden mit Zeitstempel und Staff-UUID in der Datenbank geloggt. Dies ist primär für administrative Zwecke und kann später über das Webpanel oder spezielle Admin-Befehle ausgewertet werden.
* **Automatisierte Vorschläge (Zukunft mit Report-System):**
    * Wenn `/punish <SpielerName>` geöffnet wird und ein (zukünftiges) Report-System offene, unbearbeitete Reports für diesen Spieler hat, könnte die GUI dies anzeigen.
    * Optional: Basierend auf Keywords im Report könnte ein passendes Template *vorgeschlagen* werden.

## 8. Datenbankstruktur (MariaDB - Vorschlag)

* **`asmp_punishments`**:
    * `id` (INT, PK, AI)
    * `player_uuid` (VARCHAR(36), INDEX)
    * `punishment_type` (ENUM('MUTE', 'VOICE_MUTE', 'BAN'))
    * `reason` (TEXT) - Der dem Spieler angezeigte Grund
    * `staff_uuid` (VARCHAR(36))
    * `internal_note` (TEXT, NULLABLE)
    * `template_name` (VARCHAR(255), NULLABLE) - Name des verwendeten Templates
    * `template_stufe_id` (INT, NULLABLE) - ID der Stufe innerhalb des Templates
    * `start_time` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
    * `end_time` (TIMESTAMP, NULLABLE) - NULL für permanente Strafen
    * `duration_string` (VARCHAR(50)) - z.B. "30m", "permanent"
    * `status` (ENUM('ACTIVE', 'EXPIRED', 'PARDONED'), DEFAULT 'ACTIVE')
    * `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
    * `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
* **`asmp_ip_bans`**:
    * `id` (INT, PK, AI)
    * `ip_address` (VARCHAR(45), INDEX) - Unterstützt IPv4 und IPv6
    * `reason` (TEXT)
    * `staff_uuid` (VARCHAR(36))
    * `linked_player_uuid` (VARCHAR(36), NULLABLE) - UUID des Spielers, der den IP-Ban primär ausgelöst hat
    * `start_time` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
    * `end_time` (TIMESTAMP, NULLABLE)
    * `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
* **`asmp_staff_actions_log`**:
    * `id` (INT, PK, AI)
    * `staff_uuid` (VARCHAR(36))
    * `action_type` (VARCHAR(255)) - z.B. "PUNISHMENT_CREATE", "PUNISHMENT_PARDON", "PUNISHMENT_EDIT"
    * `target_player_uuid` (VARCHAR(36), NULLABLE)
    * `punishment_id` (INT, NULLABLE, FK zu `asmp_punishments.id`)
    * `details` (JSON oder TEXT) - Zusätzliche Infos zur Aktion
    * `timestamp` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)

---

Dieses Markdown-Dokument fasst alle besprochenen Punkte zusammen und sollte eine gute Grundlage für die Entwicklung des Punishment-Moduls für AdventureSMP bieten.
```

Ich hoffe, das ist genau das, was du brauchst!