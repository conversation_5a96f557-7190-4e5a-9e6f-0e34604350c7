package de.dasjeff.aSMPVCore.managers;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.ConfigManager.ConfigWrapper;
import de.dasjeff.aSMPVCore.util.ConfigValidator;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * Manages both local (Caffeine) and distributed (Redis) caching for the ASMP-VCore plugin.
 * Provides a unified interface for caching operations across the network.
 */
public class CacheManager {

    private final ASMPVCore corePlugin;
    private final ConcurrentMap<String, Cache<Object, Object>> localCaches = new ConcurrentHashMap<>();
    private final boolean enableStatistics;
    private final boolean redisEnabled;

    // Redis configuration
    private JedisPool jedisPool;
    private final String redisHost;
    private final int redisPort;
    private final String redisPassword;
    private final int redisDatabase;
    private final int redisTimeout;

    public CacheManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;

        ConfigWrapper config = corePlugin.getConfigManager().getMainConfig();
        this.enableStatistics = config.getBoolean("cache.enable_statistics", false);

        // Redis configuration
        this.redisEnabled = config.getBoolean("redis.enabled", true);
        this.redisHost = config.getString("redis.host", "localhost");
        this.redisPort = config.getInt("redis.port", 6379);
        this.redisPassword = config.getString("redis.password", "");
        this.redisDatabase = config.getInt("redis.database", 0);
        this.redisTimeout = config.getInt("redis.timeout", 2000);

        if (redisEnabled) {
            setupRedis();
        }
    }

    private void setupRedis() {
        try {
            // Validate Redis configuration first
            ConfigValidator.validateRedisConfig(corePlugin.getConfigManager().getMainConfig());

            JedisPoolConfig poolConfig = new JedisPoolConfig();
            ConfigWrapper config = corePlugin.getConfigManager().getMainConfig();

            poolConfig.setMaxTotal(config.getInt("redis.pool_max_total", 20));
            poolConfig.setMaxIdle(config.getInt("redis.pool_max_idle", 10));
            poolConfig.setMinIdle(config.getInt("redis.pool_min_idle", 2));
            poolConfig.setTestOnBorrow(true);
            poolConfig.setTestOnReturn(true);
            poolConfig.setTestWhileIdle(true);
            poolConfig.setMinEvictableIdleDuration(Duration.ofMinutes(1));
            poolConfig.setTimeBetweenEvictionRuns(Duration.ofSeconds(30));

            if (redisPassword.isEmpty()) {
                jedisPool = new JedisPool(poolConfig, redisHost, redisPort, redisTimeout);
            } else {
                jedisPool = new JedisPool(poolConfig, redisHost, redisPort, redisTimeout, redisPassword, redisDatabase);
            }

            // Test Redis connection
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.ping();
                corePlugin.getLogger().info("Redis connection established successfully");
            }
        } catch (IllegalArgumentException e) {
            corePlugin.getLogger().error("Redis configuration validation failed: {}", e.getMessage());
            jedisPool = null;
        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to setup Redis connection", e);
            jedisPool = null;
        }
    }

    /**
     * Creates a new local Caffeine cache with the specified parameters.
     */
    @SuppressWarnings("unchecked")
    public <K, V> Cache<K, V> createLocalCache(String cacheName, long maximumSize, long expireAfterAccess, TimeUnit timeUnit) {
        if (localCaches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);

        if (enableStatistics) {
            builder.recordStats();
        }

        Cache<Object, Object> newCache = builder.build();
        localCaches.put(cacheName, newCache);
        return (Cache<K, V>) newCache;
    }

    /**
     * Creates a loading cache with automatic value computation.
     */
    @SuppressWarnings("unchecked")
    public <K, V> LoadingCache<K, V> createLoadingCache(String cacheName, long maximumSize,
                                                       long expireAfterAccess, TimeUnit timeUnit,
                                                       CacheLoader<K, V> loader) {
        if (localCaches.containsKey(cacheName)) {
            throw new IllegalArgumentException("Cache with name '" + cacheName + "' already exists.");
        }

        Caffeine<Object, Object> builder = Caffeine.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(expireAfterAccess, timeUnit);

        if (enableStatistics) {
            builder.recordStats();
        }

        LoadingCache<K, V> loadingCache = (LoadingCache<K, V>) builder.build((CacheLoader<Object, Object>) loader);
        localCaches.put(cacheName, (Cache<Object, Object>) loadingCache);
        return loadingCache;
    }

    /**
     * Gets a local cache by name.
     */
    @SuppressWarnings("unchecked")
    public <K, V> Cache<K, V> getLocalCache(String cacheName) {
        return (Cache<K, V>) localCaches.get(cacheName);
    }

    /**
     * Gets a value from local cache with fallback function.
     */
    public <K, V> V getLocal(String cacheName, K key, Function<K, V> fallback) {
        Cache<K, V> cache = getLocalCache(cacheName);
        if (cache == null) {
            return fallback != null ? fallback.apply(key) : null;
        }

        V value = (V) cache.getIfPresent(key);
        if (value == null && fallback != null) {
            value = fallback.apply(key);
            if (value != null) {
                cache.put(key, value);
            }
        }
        return value;
    }

    /**
     * Puts a value into local cache.
     */
    public <K, V> void putLocal(String cacheName, K key, V value) {
        Cache<K, V> cache = getLocalCache(cacheName);
        if (cache != null) {
            cache.put(key, value);
        }
    }

    /**
     * Removes a value from local cache.
     */
    public void invalidateLocal(String cacheName, Object key) {
        Cache<Object, Object> cache = localCaches.get(cacheName);
        if (cache != null) {
            cache.invalidate(key);
        }
    }

    /**
     * Gets a value from Redis cache.
     */
    public String getRedis(String key) {
        if (!isRedisAvailable() || !isValidRedisKey(key)) {
            return null;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        } catch (JedisException e) {
            corePlugin.getLogger().error("Redis GET operation failed for key: {}", key, e);
            return null;
        }
    }

    /**
     * Puts a value into Redis cache with expiration.
     */
    public void putRedis(String key, String value, int expireSeconds) {
        if (!isRedisAvailable() || !isValidRedisKey(key) || !isValidRedisValue(value)) {
            return;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            if (expireSeconds > 0) {
                jedis.setex(key, expireSeconds, value);
            } else {
                jedis.set(key, value);
            }
        } catch (JedisException e) {
            corePlugin.getLogger().error("Redis SET operation failed for key: {}", key, e);
        }
    }

    /**
     * Removes a value from Redis cache.
     */
    public void deleteRedis(String key) {
        if (!isRedisAvailable()) {
            return;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            jedis.del(key);
        } catch (JedisException e) {
            corePlugin.getLogger().error("Redis DEL operation failed for key: {}", key, e);
        }
    }

    /**
     * Publishes a message to a Redis channel.
     */
    public void publishRedis(String channel, String message) {
        if (!isRedisAvailable()) {
            return;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            jedis.publish(channel, message);
        } catch (JedisException e) {
            corePlugin.getLogger().error("Redis PUBLISH operation failed for channel: {}", channel, e);
        }
    }

    /**
     * Checks if Redis is available.
     */
    public boolean isRedisAvailable() {
        return redisEnabled && jedisPool != null && !jedisPool.isClosed();
    }

    /**
     * Gets cache statistics for a local cache.
     */
    public String getCacheStats(String cacheName) {
        Cache<Object, Object> cache = localCaches.get(cacheName);
        if (cache == null) {
            return "Cache not found: " + cacheName;
        }

        if (enableStatistics) {
            var stats = cache.stats();
            return String.format("Cache '%s' - Size: %d, Hits: %d, Misses: %d, Hit Rate: %.2f%%",
                    cacheName, cache.estimatedSize(), stats.hitCount(), stats.missCount(), stats.hitRate() * 100);
        } else {
            return String.format("Cache '%s' - Size: %d (Statistics disabled)", cacheName, cache.estimatedSize());
        }
    }

    /**
     * Cleans up and removes all caches. Should be called on plugin disable.
     */
    public void shutdown() {
        try {
            // Clean up local caches
            localCaches.values().forEach(cache -> {
                try {
                    cache.invalidateAll();
                    cache.cleanUp();
                } catch (Exception e) {
                    corePlugin.getLogger().error("Error cleaning up local cache during shutdown", e);
                }
            });
            localCaches.clear();

            // Close Redis pool
            if (jedisPool != null && !jedisPool.isClosed()) {
                jedisPool.close();
                corePlugin.getLogger().info("Redis connection pool closed.");
            }

            corePlugin.getLogger().info("All caches have been invalidated and cleared.");
        } catch (Exception e) {
            corePlugin.getLogger().error("Error during cache shutdown", e);
        }
    }

    /**
     * Validates a Redis key for security and format.
     * @param key The key to validate.
     * @return true if the key is valid, false otherwise.
     */
    private boolean isValidRedisKey(String key) {
        if (key == null || key.isEmpty()) {
            return false;
        }

        // Check key length (Redis has a limit of 512MB, but we use a reasonable limit)
        if (key.length() > 250) {
            corePlugin.getLogger().warn("Redis key too long: {}", key.length());
            return false;
        }

        // Check for dangerous characters
        char[] dangerousChars = {'\n', '\r', '\t', '\0', ' '};
        for (char c : dangerousChars) {
            if (key.indexOf(c) != -1) {
                corePlugin.getLogger().warn("Redis key contains dangerous character: {}", (int) c);
                return false;
            }
        }

        return true;
    }

    /**
     * Validates a Redis value for security and format.
     * @param value The value to validate.
     * @return true if the value is valid, false otherwise.
     */
    private boolean isValidRedisValue(String value) {
        if (value == null) {
            return true; // Null values are acceptable
        }

        // Check value size (Redis has a limit of 512MB, but we use a reasonable limit)
        if (value.length() > 1048576) { // 1MB limit
            corePlugin.getLogger().warn("Redis value too large: {} bytes", value.length());
            return false;
        }

        return true;
    }
}
